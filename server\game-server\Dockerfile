# 构建阶段
FROM node:22-alpine AS build

WORKDIR /app

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm ci

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 运行阶段
FROM node:22-alpine

WORKDIR /app

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装生产依赖
RUN npm ci --only=production

# 从构建阶段复制构建结果
COPY --from=build /app/dist ./dist
COPY --from=build /app/.env* ./

# 设置环境变量
ENV NODE_ENV=production

# 暴露端口
EXPOSE 3030 3003

# 启动应用
CMD ["node", "dist/main"]
