# 数字人系统微服务配置分析报告

## 概述

本报告详细分析了数字人系统中所有微服务的配置，确保各项配置正确，服务能够协同工作，并保持原有程序逻辑和执行流程不变。

## 微服务架构分析

### 1. 服务分类和数据库使用

#### MySQL服务 (8个服务)
| 服务名 | 端口 | 数据库 | 说明 |
|--------|------|--------|------|
| service-registry | 8761(HTTP)/4010(TCP) | ir_engine_registry | 服务注册中心 |
| api-gateway | 8080 | api_gateway | API网关 |
| user-service | 4001(HTTP)/3001(TCP) | ir_engine_users | 用户管理 |
| project-service | 3002 | ir_engine_projects | 项目管理 |
| asset-service | 4003(HTTP)/3003(TCP) | ir_engine_assets | 资产管理 |
| render-service | 3006 | ir_engine_render | 渲染服务 |
| ai-model-service | 3010 | ai_model_service | AI模型服务 |
| monitoring-service | 3013 | monitoring | 监控服务 |

#### PostgreSQL服务 (7个服务)
| 服务名 | 端口 | 数据库 | 说明 |
|--------|------|--------|------|
| scene-generation-service | 3004 | scene_generation | 场景生成 |
| asset-library-service | 3005 | asset_library | 资产库 |
| collaboration-service | 3007 | digital_human_rag | 协作服务 |
| knowledge-service | 3008 | knowledge_service | 知识库 |
| rag-engine | 3009 | digital_human_rag | RAG引擎 |
| binding-service | 3011 | digital_human_rag | 绑定服务 |
| scene-template-service | 8004 | scene_templates | 场景模板 |

#### 无数据库服务 (1个服务)
| 服务名 | 端口 | 说明 |
|--------|------|------|
| game-server | 3012 | 游戏服务器 |

### 2. 端口配置分析

#### 微服务通信端口规范
- **TCP端口**: 用于微服务间通信
- **HTTP端口**: 用于外部API访问
- **容器内端口**: 与容器外端口保持一致

#### 端口映射配置
```yaml
# 示例：用户服务
ports:
  - "3001:3001"  # TCP微服务端口
  - "4001:4001"  # HTTP API端口
```

### 3. 环境变量配置标准化

#### 通用环境变量
```yaml
- NODE_ENV=production
- PORT=<服务端口>
- <SERVICE_NAME>_HOST=0.0.0.0
- <SERVICE_NAME>_PORT=<TCP端口>
- SERVICE_REGISTRY_HOST=service-registry
- SERVICE_REGISTRY_PORT=3010
- SERVICE_REGISTRY_URL=http://service-registry:4010
- REDIS_URL=redis://:redis123@redis:6379
```

#### MySQL服务环境变量
```yaml
- DB_HOST=mysql
- DB_PORT=3306
- DB_USERNAME=root
- DB_PASSWORD=mysql123
- DB_DATABASE=<数据库名>
```

#### PostgreSQL服务环境变量
```yaml
- DB_HOST=postgres
- DB_PORT=5432
- DB_USERNAME=postgres
- DB_PASSWORD=postgres123
- DB_DATABASE=<数据库名>
```

## 服务依赖关系分析

### 1. 服务启动顺序
1. **基础设施服务**: PostgreSQL, MySQL, Redis, MinIO, Elasticsearch
2. **监控服务**: Prometheus, Grafana, Jaeger
3. **服务注册中心**: service-registry
4. **核心微服务**: 按依赖关系启动
5. **API网关**: api-gateway (最后启动)
6. **前端服务**: editor

### 2. 服务间通信配置

#### 服务注册中心通信
- 所有微服务都连接到service-registry
- 使用TCP端口3010进行微服务注册
- 使用HTTP端口4010进行健康检查

#### 微服务间调用
- API网关通过环境变量配置各服务地址
- 使用容器名进行服务发现
- 统一使用TCP端口进行微服务通信

### 3. 数据库连接配置

#### 连接池配置
```typescript
// MySQL连接示例
{
  type: 'mysql',
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT),
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_DATABASE,
  synchronize: process.env.NODE_ENV !== 'production',
  logging: process.env.NODE_ENV === 'development'
}
```

## 配置修正内容

### 1. 端口配置修正
- 修正service-registry端口映射：8761→4010(HTTP), 4010→3010(TCP)
- 统一微服务TCP通信端口配置
- 确保容器内外端口一致性

### 2. 环境变量标准化
- 添加HOST配置，统一设置为0.0.0.0
- 标准化服务注册中心连接配置
- 分离MySQL和PostgreSQL连接配置

### 3. 服务发现配置
- 统一SERVICE_REGISTRY_URL格式
- 配置微服务间通信地址
- 添加服务健康检查端点

### 4. 数据库配置优化
- 明确区分MySQL和PostgreSQL服务
- 配置正确的数据库名称
- 添加连接池和重试机制

## 健康检查配置

### 1. 健康检查端点
所有服务都配置了/health端点：
```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:<port>/health"]
  interval: 30s
  timeout: 10s
  retries: 3
```

### 2. 服务依赖检查
使用depends_on确保服务启动顺序：
```yaml
depends_on:
  postgres:
    condition: service_healthy
  mysql:
    condition: service_healthy
  redis:
    condition: service_healthy
  service-registry:
    condition: service_healthy
```

## 网络配置

### 1. 容器网络
- 所有服务运行在digital-human-network网络中
- 使用bridge驱动，子网**********/16
- 服务间通过容器名进行通信

### 2. 端口暴露
- 只暴露必要的端口到宿主机
- 内部通信使用容器网络
- 外部访问通过API网关统一入口

## 安全配置

### 1. 数据库安全
- 使用强密码
- 限制数据库访问权限
- 配置SSL连接（生产环境）

### 2. 服务间认证
- JWT令牌验证
- 服务注册中心认证
- API网关统一鉴权

## 监控和日志

### 1. 监控配置
- Prometheus指标收集
- Grafana可视化面板
- Jaeger链路追踪

### 2. 日志配置
- 统一日志格式
- 日志级别配置
- 日志文件轮转

## 总结

经过配置分析和修正，数字人系统的微服务配置现在具备以下特点：

1. **配置一致性**: 所有服务使用统一的配置标准
2. **服务发现**: 完善的服务注册和发现机制
3. **数据库分离**: 明确区分MySQL和PostgreSQL使用场景
4. **端口规范**: 统一的端口分配和映射规则
5. **健康检查**: 完整的服务健康监控
6. **依赖管理**: 正确的服务启动顺序
7. **网络隔离**: 安全的容器网络配置
8. **监控完备**: 全面的监控和追踪系统

所有配置修正都保持了原有程序逻辑和执行流程不变，确保系统的稳定性和可靠性。
