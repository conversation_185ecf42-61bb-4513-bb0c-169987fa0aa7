﻿#!/usr/bin/env pwsh
# Windows Docker Compose 停止脚本
# 使用方法: .\stop-windows.ps1 [选项]

param(
    [switch]$Clean,           # 清理容器和网络
    [switch]$Volumes,         # 同时删除数据卷
    [switch]$Images,          # 同时删除镜像
    [switch]$All,             # 完全清理（容器+卷+镜像+网络）
    [switch]$Force,           # 强制停止
    [switch]$Help,            # 显示帮助信息
    [string]$Service = "",    # 停止特定服务
    [int]$Timeout = 30        # 停止超时时间（秒）
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput Cyan "ℹ️  $message"
}

function Write-Success($message) {
    Write-ColorOutput Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "❌ $message"
}

function Write-Header($message) {
    Write-Host ""
    Write-ColorOutput Magenta "🛑 $message"
    Write-Host ("=" * 60)
}

# 显示帮助信息
function Show-Help {
    Write-Host "Windows Docker Compose 停止脚本"
    Write-Host ""
    Write-Host "用法: .\stop-windows.ps1 [选项]"
    Write-Host ""
    Write-Host "选项:"
    Write-Host "  -Clean          停止并删除容器和网络"
    Write-Host "  -Volumes        同时删除数据卷（注意：会丢失数据！）"
    Write-Host "  -Images         同时删除相关镜像"
    Write-Host "  -All            完全清理（容器+卷+镜像+网络）"
    Write-Host "  -Force          强制停止容器"
    Write-Host "  -Help           显示此帮助信息"
    Write-Host "  -Service [名称] 仅停止指定服务"
    Write-Host "  -Timeout [秒]   设置停止超时时间（默认30秒）"
    Write-Host ""
    Write-Host "示例:"
    Write-Host "  .\stop-windows.ps1                     # 停止所有服务"
    Write-Host "  .\stop-windows.ps1 -Clean              # 停止并清理容器"
    Write-Host "  .\stop-windows.ps1 -Service mysql      # 仅停止MySQL服务"
    Write-Host "  .\stop-windows.ps1 -All                # 完全清理所有资源"
    Write-Host "  .\stop-windows.ps1 -Force -Timeout 10  # 强制停止（10秒超时）"
    Write-Host ""
    Write-Host "警告:"
    Write-Host "  使用 -Volumes 或 -All 选项会删除所有数据，请确保已备份重要数据！"
}

# 获取Docker Compose命令
function Get-DockerComposeCommand {
    try {
        docker-compose --version | Out-Null
        return "docker-compose"
    } catch {
        return "docker compose"
    }
}

# 检查Docker是否运行
function Test-DockerRunning {
    try {
        docker info | Out-Null
        return $true
    } catch {
        return $false
    }
}

# 获取运行中的容器
function Get-RunningContainers($composeCmd) {
    try {
        $containers = & $composeCmd -f docker-compose.windows.yml ps -q
        return $containers
    } catch {
        return @()
    }
}

# 停止特定服务
function Stop-SpecificService($composeCmd, $serviceName) {
    Write-Header "停止服务: $serviceName"
    
    try {
        if ($Force) {
            Write-Info "强制停止服务: $serviceName"
            & $composeCmd -f docker-compose.windows.yml kill $serviceName
        } else {
            Write-Info "优雅停止服务: $serviceName (超时: ${Timeout}秒)"
            & $composeCmd -f docker-compose.windows.yml stop -t $Timeout $serviceName
        }
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "服务 $serviceName 已停止"
        } else {
            Write-Warning "停止服务 $serviceName 时出现警告"
        }
    } catch {
        Write-Error "停止服务 $serviceName 失败: $($_.Exception.Message)"
    }
}

# 停止所有服务
function Stop-AllServices($composeCmd) {
    Write-Header "停止所有服务"
    
    $containers = Get-RunningContainers $composeCmd
    
    if ($containers.Count -eq 0) {
        Write-Info "没有运行中的容器"
        return
    }
    
    Write-Info "发现 $($containers.Count) 个运行中的容器"
    
    try {
        if ($Force) {
            Write-Warning "强制停止所有容器..."
            & $composeCmd -f docker-compose.windows.yml kill
        } else {
            Write-Info "优雅停止所有容器 (超时: ${Timeout}秒)..."
            & $composeCmd -f docker-compose.windows.yml stop -t $Timeout
        }
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "所有服务已停止"
        } else {
            Write-Warning "停止服务时出现警告"
        }
    } catch {
        Write-Error "停止服务失败: $($_.Exception.Message)"
    }
}

# 清理容器和网络
function Remove-ContainersAndNetworks($composeCmd) {
    Write-Header "清理容器和网络"
    
    try {
        Write-Info "删除容器和网络..."
        & $composeCmd -f docker-compose.windows.yml down --remove-orphans
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "容器和网络已清理"
        } else {
            Write-Warning "清理容器和网络时出现警告"
        }
    } catch {
        Write-Error "清理失败: $($_.Exception.Message)"
    }
}

# 删除数据卷
function Remove-Volumes($composeCmd) {
    Write-Header "删除数据卷"
    
    Write-Warning "⚠️  即将删除所有数据卷，这将导致数据丢失！"
    Write-Warning "包括数据库数据、上传文件、缓存等所有持久化数据"
    
    $confirmation = Read-Host "确认删除所有数据卷？输入 'YES' 确认"
    
    if ($confirmation -eq "YES") {
        try {
            Write-Info "删除数据卷..."
            & $composeCmd -f docker-compose.windows.yml down -v --remove-orphans
            
            if ($LASTEXITCODE -eq 0) {
                Write-Success "数据卷已删除"
            } else {
                Write-Warning "删除数据卷时出现警告"
            }
        } catch {
            Write-Error "删除数据卷失败: $($_.Exception.Message)"
        }
    } else {
        Write-Info "取消删除数据卷操作"
    }
}

# 删除镜像
function Remove-Images($composeCmd) {
    Write-Header "删除相关镜像"
    
    try {
        # 获取项目相关的镜像
        $images = docker images --filter "label=com.docker.compose.project=dl-engine" -q
        
        if ($images.Count -eq 0) {
            Write-Info "没有找到相关镜像"
            return
        }
        
        Write-Info "发现 $($images.Count) 个相关镜像"
        Write-Warning "即将删除这些镜像，下次启动时需要重新构建"
        
        $confirmation = Read-Host "确认删除镜像？输入 'YES' 确认"
        
        if ($confirmation -eq "YES") {
            Write-Info "删除镜像..."
            docker rmi $images -f
            Write-Success "镜像已删除"
        } else {
            Write-Info "取消删除镜像操作"
        }
    } catch {
        Write-Error "删除镜像失败: $($_.Exception.Message)"
    }
}

# 系统清理
function Invoke-SystemCleanup {
    Write-Header "系统清理"
    
    try {
        Write-Info "清理未使用的Docker资源..."
        docker system prune -f
        
        Write-Info "清理未使用的网络..."
        docker network prune -f
        
        Write-Info "清理未使用的卷..."
        docker volume prune -f
        
        Write-Success "系统清理完成"
    } catch {
        Write-Error "系统清理失败: $($_.Exception.Message)"
    }
}

# 显示清理后状态
function Show-CleanupStatus($composeCmd) {
    Write-Header "清理后状态"
    
    try {
        Write-Info "剩余容器:"
        docker ps -a --filter "label=com.docker.compose.project=dl-engine"
        
        Write-Info "剩余镜像:"
        docker images --filter "label=com.docker.compose.project=dl-engine"
        
        Write-Info "剩余卷:"
        docker volume ls --filter "label=com.docker.compose.project=dl-engine"
        
        Write-Info "剩余网络:"
        docker network ls --filter "label=com.docker.compose.project=dl-engine"
    } catch {
        Write-Warning "无法获取状态信息"
    }
}

# 主函数
function Main {
    if ($Help) {
        Show-Help
        return
    }
    
    Write-Header "DL Engine Windows Docker Compose 停止"
    
    # 检查Docker状态
    if (-not (Test-DockerRunning)) {
        Write-Warning "Docker Desktop 未运行，无需停止服务"
        return
    }
    
    $composeCmd = Get-DockerComposeCommand
    Write-Success "Docker Compose 可用 ($composeCmd)"
    
    # 检查是否存在配置文件
    if (-not (Test-Path "docker-compose.windows.yml")) {
        Write-Error "找不到 docker-compose.windows.yml 文件"
        exit 1
    }
    
    # 停止特定服务
    if ($Service) {
        Stop-SpecificService $composeCmd $Service
        return
    }
    
    # 停止所有服务
    Stop-AllServices $composeCmd
    
    # 根据选项执行清理操作
    if ($All) {
        Write-Warning "执行完全清理（容器+卷+镜像+网络）"
        Remove-Volumes $composeCmd
        Remove-Images $composeCmd
        Invoke-SystemCleanup
    } else {
        if ($Clean) {
            Remove-ContainersAndNetworks $composeCmd
        }
        
        if ($Volumes) {
            Remove-Volumes $composeCmd
        }
        
        if ($Images) {
            Remove-Images $composeCmd
        }
    }
    
    Write-Success "🎉 停止操作完成！"
    
    # 显示清理后状态
    if ($Clean -or $All) {
        Show-CleanupStatus $composeCmd
    }
    
    # 提示信息
    Write-Info "💡 重新启动请运行: .\start-windows.ps1"
    Write-Info "💡 查看帮助请运行: .\start-windows.ps1 -Help"
    
    if ($Volumes -or $All) {
        Write-Warning "⚠️  数据卷已删除，下次启动将是全新环境"
    }
}

# 执行主函数
try {
    Main
} catch {
    Write-Error "脚本执行失败: $($_.Exception.Message)"
    exit 1
}
