version: '3.8'

services:
  # ===========================================
  # 基础设施服务
  # ===========================================
  
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: digital-human-postgres
    environment:
      POSTGRES_DB: digital_human_rag
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./shared/init-scripts:/docker-entrypoint-initdb.d
    networks:
      - digital-human-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: digital-human-mysql
    environment:
      MYSQL_ROOT_PASSWORD: mysql123
      MYSQL_ALLOW_EMPTY_PASSWORD: 'no'
      MYSQL_DATABASE: digital_human_system
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./shared/init-scripts/mysql:/docker-entrypoint-initdb.d
    networks:
      - digital-human-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-pmysql123"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: digital-human-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - digital-human-network
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis123
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "redis123", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # MinIO对象存储
  minio:
    image: minio/minio:latest
    container_name: digital-human-minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    networks:
      - digital-human-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Elasticsearch搜索引擎
  elasticsearch:
    image: elasticsearch:8.8.0
    container_name: digital-human-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - bootstrap.memory_lock=true
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - digital-human-network
    restart: unless-stopped
    ulimits:
      memlock:
        soft: -1
        hard: -1
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # ===========================================
  # 监控服务
  # ===========================================
  
  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: digital-human-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - digital-human-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: digital-human-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - digital-human-network
    restart: unless-stopped

  # Jaeger链路追踪
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: digital-human-jaeger
    ports:
      - "16686:16686"
      - "14268:14268"
      - "6831:6831/udp"
      - "6832:6832/udp"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - digital-human-network
    restart: unless-stopped

  # ===========================================
  # 核心微服务
  # ===========================================

  # 服务注册中心
  service-registry:
    build:
      context: ./service-registry
      dockerfile: Dockerfile
    container_name: service-registry
    ports:
      - "8761:4010"
      - "4010:3010"
    environment:
      - NODE_ENV=production
      - PORT=4010
      - SERVICE_REGISTRY_PORT=3010
      - SERVICE_REGISTRY_HTTP_PORT=4010
      - SERVICE_REGISTRY_HOST=0.0.0.0
      - REDIS_URL=redis://:redis123@redis:6379
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=mysql123
      - DB_DATABASE=ir_engine_registry
    depends_on:
      postgres:
        condition: service_healthy
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - digital-human-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8761/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API网关
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    container_name: api-gateway
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - PORT=8080
      - API_GATEWAY_PORT=8080
      - SERVICE_REGISTRY_URL=http://service-registry:4010
      - REDIS_URL=redis://:redis123@redis:6379
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
      - PROJECT_SERVICE_HOST=project-service
      - PROJECT_SERVICE_PORT=3002
      - ASSET_SERVICE_HOST=asset-service
      - ASSET_SERVICE_PORT=3003
      - SCENE_GENERATION_SERVICE_HOST=scene-generation-service
      - SCENE_GENERATION_SERVICE_PORT=3004
      - ASSET_LIBRARY_SERVICE_HOST=asset-library-service
      - ASSET_LIBRARY_SERVICE_PORT=3005
      - RENDER_SERVICE_HOST=render-service
      - RENDER_SERVICE_PORT=3006
      - COLLABORATION_SERVICE_HOST=collaboration-service
      - COLLABORATION_SERVICE_PORT=3007
      - KNOWLEDGE_SERVICE_HOST=knowledge-service
      - KNOWLEDGE_SERVICE_PORT=3008
      - RAG_ENGINE_HOST=rag-engine
      - RAG_ENGINE_PORT=3009
      - AI_MODEL_SERVICE_HOST=ai-model-service
      - AI_MODEL_SERVICE_PORT=3010
      - BINDING_SERVICE_HOST=binding-service
      - BINDING_SERVICE_PORT=3011
      - GAME_SERVER_HOST=game-server
      - GAME_SERVER_PORT=3012
      - MONITORING_SERVICE_HOST=monitoring-service
      - MONITORING_SERVICE_PORT=3013
      - SCENE_TEMPLATE_SERVICE_HOST=scene-template-service
      - SCENE_TEMPLATE_SERVICE_PORT=8004
    depends_on:
      service-registry:
        condition: service_healthy
      postgres:
        condition: service_healthy
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - digital-human-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 用户服务
  user-service:
    build:
      context: ./user-service
      dockerfile: Dockerfile
    container_name: user-service
    ports:
      - "3001:3001"
      - "4001:4001"
    environment:
      - NODE_ENV=production
      - PORT=4001
      - USER_SERVICE_HOST=0.0.0.0
      - USER_SERVICE_PORT=3001
      - USER_SERVICE_HTTP_PORT=4001
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - SERVICE_REGISTRY_URL=http://service-registry:4010
      - REDIS_URL=redis://:redis123@redis:6379
      - JWT_SECRET=user-service-jwt-secret-key-2025
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=mysql123
      - DB_DATABASE=ir_engine_users
    depends_on:
      postgres:
        condition: service_healthy
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    networks:
      - digital-human-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 项目服务
  project-service:
    build:
      context: ./project-service
      dockerfile: Dockerfile
    container_name: project-service
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=production
      - PORT=3002
      - PROJECT_SERVICE_HOST=0.0.0.0
      - PROJECT_SERVICE_PORT=3002
      - PROJECT_SERVICE_HTTP_PORT=3002
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - SERVICE_REGISTRY_URL=http://service-registry:4010
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
      - REDIS_URL=redis://:redis123@redis:6379
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=mysql123
      - DB_DATABASE=ir_engine_projects
    depends_on:
      postgres:
        condition: service_healthy
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    networks:
      - digital-human-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 资产服务
  asset-service:
    build:
      context: ./asset-service
      dockerfile: Dockerfile
    container_name: asset-service
    ports:
      - "3003:3003"
      - "4003:4003"
    environment:
      - NODE_ENV=production
      - PORT=4003
      - ASSET_SERVICE_HOST=0.0.0.0
      - ASSET_SERVICE_PORT=3003
      - ASSET_SERVICE_HTTP_PORT=4003
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - SERVICE_REGISTRY_URL=http://service-registry:4010
      - REDIS_URL=redis://:redis123@redis:6379
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin123
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=mysql123
      - DB_DATABASE=ir_engine_assets
    depends_on:
      postgres:
        condition: service_healthy
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    networks:
      - digital-human-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4003/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 场景生成服务
  scene-generation-service:
    build:
      context: ./scene-generation-service
      dockerfile: Dockerfile
    container_name: scene-generation-service
    ports:
      - "3004:3004"
    environment:
      - NODE_ENV=production
      - PORT=3004
      - SCENE_GENERATION_SERVICE_HOST=0.0.0.0
      - SCENE_GENERATION_SERVICE_PORT=3004
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - SERVICE_REGISTRY_URL=http://service-registry:4010
      - DATABASE_URL=***********************************************/digital_human_rag
      - REDIS_URL=redis://:redis123@redis:6379
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin123
      - AI_MODEL_SERVICE_URL=http://ai-model-service:3010
      - ASSET_LIBRARY_SERVICE_URL=http://asset-library-service:3005
      - SCENE_TEMPLATE_SERVICE_URL=http://scene-template-service:8004
      - UPLOAD_PATH=./uploads
      - BASE_URL=http://localhost:3004
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=postgres
      - DB_PASSWORD=postgres123
      - DB_DATABASE=scene_generation
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    volumes:
      - ./scene-generation-service/uploads:/app/uploads
      - ./scene-generation-service/logs:/app/logs
    networks:
      - digital-human-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3004/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 资产库服务
  asset-library-service:
    build:
      context: ./asset-library-service
      dockerfile: Dockerfile
    container_name: asset-library-service
    ports:
      - "3005:3005"
    environment:
      - NODE_ENV=production
      - PORT=3005
      - ASSET_LIBRARY_SERVICE_HOST=0.0.0.0
      - ASSET_LIBRARY_SERVICE_PORT=3005
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - SERVICE_REGISTRY_URL=http://service-registry:4010
      - DATABASE_URL=***********************************************/digital_human_rag
      - REDIS_URL=redis://:redis123@redis:6379
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin123
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=postgres
      - DB_PASSWORD=postgres123
      - DB_DATABASE=asset_library
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    networks:
      - digital-human-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3005/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 渲染服务
  render-service:
    build:
      context: ./render-service
      dockerfile: Dockerfile
    container_name: render-service
    ports:
      - "3006:3006"
    environment:
      - NODE_ENV=production
      - PORT=3006
      - RENDER_SERVICE_HOST=0.0.0.0
      - RENDER_SERVICE_PORT=3006
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - SERVICE_REGISTRY_URL=http://service-registry:4010
      - REDIS_URL=redis://:redis123@redis:6379
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin123
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=mysql123
      - DB_DATABASE=ir_engine_render
    depends_on:
      postgres:
        condition: service_healthy
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    networks:
      - digital-human-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3006/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 协作服务
  collaboration-service:
    build:
      context: ./collaboration-service
      dockerfile: Dockerfile
    container_name: collaboration-service
    ports:
      - "3007:3007"
    environment:
      - NODE_ENV=production
      - PORT=3007
      - COLLABORATION_SERVICE_HOST=0.0.0.0
      - COLLABORATION_SERVICE_PORT=3007
      - DATABASE_URL=***********************************************/digital_human_rag
      - REDIS_URL=redis://:redis123@redis:6379
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=postgres
      - DB_PASSWORD=postgres123
      - DB_DATABASE=digital_human_rag
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    networks:
      - digital-human-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3007/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 知识服务
  knowledge-service:
    build:
      context: ./knowledge-service
      dockerfile: Dockerfile
    container_name: knowledge-service
    ports:
      - "3008:3008"
    environment:
      - NODE_ENV=production
      - PORT=3008
      - KNOWLEDGE_SERVICE_HOST=0.0.0.0
      - KNOWLEDGE_SERVICE_PORT=3008
      - KNOWLEDGE_SERVICE_HTTP_PORT=3008
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - SERVICE_REGISTRY_URL=http://service-registry:4010
      - DATABASE_URL=***********************************************/digital_human_rag
      - REDIS_URL=redis://:redis123@redis:6379
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=postgres
      - DB_PASSWORD=postgres123
      - DB_DATABASE=knowledge_service
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    networks:
      - digital-human-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3008/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # RAG引擎
  rag-engine:
    build:
      context: ./rag-engine
      dockerfile: Dockerfile
    container_name: rag-engine
    ports:
      - "3009:3009"
    environment:
      - NODE_ENV=production
      - PORT=3009
      - RAG_ENGINE_HOST=0.0.0.0
      - RAG_ENGINE_PORT=3009
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - SERVICE_REGISTRY_URL=http://service-registry:4010
      - DATABASE_URL=***********************************************/digital_human_rag
      - REDIS_URL=redis://:redis123@redis:6379
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - KNOWLEDGE_SERVICE_URL=http://knowledge-service:3008
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=postgres
      - DB_PASSWORD=postgres123
      - DB_DATABASE=digital_human_rag
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
      service-registry:
        condition: service_healthy
      knowledge-service:
        condition: service_healthy
    networks:
      - digital-human-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3009/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # AI模型服务
  ai-model-service:
    build:
      context: ./ai-model-service
      dockerfile: Dockerfile
    container_name: ai-model-service
    ports:
      - "3010:3010"
    environment:
      - NODE_ENV=production
      - PORT=3010
      - HOST=0.0.0.0
      - AI_MODEL_SERVICE_HOST=0.0.0.0
      - AI_MODEL_SERVICE_PORT=3010
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - SERVICE_REGISTRY_URL=http://service-registry:4010
      - REDIS_URL=redis://:redis123@redis:6379
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin123
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL:-https://api.openai.com/v1}
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=mysql123
      - DB_DATABASE=ai_model_service
    depends_on:
      postgres:
        condition: service_healthy
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    networks:
      - digital-human-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3010/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 绑定服务
  binding-service:
    build:
      context: ./binding-service
      dockerfile: Dockerfile
    container_name: binding-service
    ports:
      - "3011:3011"
    environment:
      - NODE_ENV=production
      - PORT=3011
      - BINDING_SERVICE_HOST=0.0.0.0
      - BINDING_SERVICE_PORT=3011
      - DATABASE_URL=***********************************************/digital_human_rag
      - REDIS_URL=redis://:redis123@redis:6379
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=postgres
      - DB_PASSWORD=postgres123
      - DB_NAME=digital_human_rag
      - DB_SSL=false
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis123
      - CACHE_TTL=3600
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    networks:
      - digital-human-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3011/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 游戏服务器
  game-server:
    build:
      context: ./game-server
      dockerfile: Dockerfile
    container_name: game-server
    ports:
      - "3012:3012"
    environment:
      - NODE_ENV=production
      - PORT=3012
      - GAME_SERVER_HOST=0.0.0.0
      - GAME_SERVER_PORT=3012
      - GAME_SERVER_MICROSERVICE_PORT=3003
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
      - PROJECT_SERVICE_HOST=project-service
      - PROJECT_SERVICE_PORT=3002
      - CORS_ORIGIN=*
      - DATABASE_URL=***********************************************/digital_human_rag
      - REDIS_URL=redis://:redis123@redis:6379
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    networks:
      - digital-human-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3012/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 监控服务
  monitoring-service:
    build:
      context: ./monitoring-service
      dockerfile: Dockerfile
    container_name: monitoring-service
    ports:
      - "3013:3013"
    environment:
      - NODE_ENV=production
      - PORT=3013
      - MONITORING_SERVICE_HOST=0.0.0.0
      - MONITORING_SERVICE_PORT=3013
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - SERVICE_REGISTRY_URL=http://service-registry:4010
      - REDIS_URL=redis://:redis123@redis:6379
      - PROMETHEUS_URL=http://prometheus:9090
      - GRAFANA_URL=http://grafana:3000
      - CORS_ORIGIN=*
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=mysql123
      - DB_DATABASE=monitoring
      - DB_SYNCHRONIZE=true
    depends_on:
      postgres:
        condition: service_healthy
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      service-registry:
        condition: service_healthy
      prometheus:
        condition: service_started
    networks:
      - digital-human-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3013/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 场景模板服务
  scene-template-service:
    build:
      context: ./scene-template-service
      dockerfile: Dockerfile
    container_name: scene-template-service
    ports:
      - "8004:8004"
    environment:
      - NODE_ENV=production
      - PORT=8004
      - SCENE_TEMPLATE_SERVICE_HOST=0.0.0.0
      - SCENE_TEMPLATE_SERVICE_PORT=8004
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - SERVICE_REGISTRY_URL=http://service-registry:4010
      - DATABASE_URL=***********************************************/digital_human_rag
      - REDIS_URL=redis://:redis123@redis:6379
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin123
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=postgres
      - DB_PASSWORD=postgres123
      - DB_DATABASE=scene_templates
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    networks:
      - digital-human-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8004/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ===========================================
  # 前端服务
  # ===========================================

  # DL引擎编辑器前端
  editor:
    build:
      context: ../editor
      dockerfile: Dockerfile
    container_name: digital-human-editor
    ports:
      - "3000:80"
    environment:
      - NODE_ENV=production
      - REACT_APP_API_BASE_URL=http://localhost:8080
      - REACT_APP_WS_URL=ws://localhost:8080
      - REACT_APP_MINIO_ENDPOINT=http://localhost:9000
    depends_on:
      api-gateway:
        condition: service_healthy
    networks:
      - digital-human-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local
  mysql_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  elasticsearch_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  digital-human-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
