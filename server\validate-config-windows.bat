@echo off
echo ========================================
echo 数字人系统配置验证 - Windows
echo ========================================

REM 检查Docker是否运行
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: Docker未运行或未安装
    pause
    exit /b 1
)

echo 正在验证配置文件...
echo.

REM 验证docker-compose文件语法
echo 验证docker-compose.windows.yml语法...
docker-compose -f docker-compose.windows.yml config >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ docker-compose.windows.yml语法正确
) else (
    echo ✗ docker-compose.windows.yml语法错误
    docker-compose -f docker-compose.windows.yml config
    pause
    exit /b 1
)

echo.
echo ========================================
echo 端口配置验证
echo ========================================

REM 检查端口冲突
echo 检查端口占用情况...

set "ports=3000 3001 3002 3003 3004 3005 3006 3007 3008 3009 3010 3011 3012 3013 4001 4003 4010 5432 6379 8004 8080 8761 9000 9001 9090 9200 9300 16686"

for %%p in (%ports%) do (
    netstat -an | find ":%%p " >nul 2>&1
    if %errorlevel% equ 0 (
        echo ⚠️  端口 %%p 已被占用
    ) else (
        echo ✓ 端口 %%p 可用
    )
)

echo.
echo ========================================
echo 服务配置验证
echo ========================================

REM 验证各服务的Dockerfile存在
echo 验证Dockerfile文件...

set "services=api-gateway user-service project-service asset-service scene-generation-service asset-library-service render-service collaboration-service knowledge-service rag-engine ai-model-service binding-service game-server monitoring-service scene-template-service service-registry"

for %%s in (%services%) do (
    if exist "%%s\Dockerfile" (
        echo ✓ %%s\Dockerfile 存在
    ) else (
        echo ✗ %%s\Dockerfile 不存在
    )
)

echo.
echo 验证package.json文件...

for %%s in (%services%) do (
    if exist "%%s\package.json" (
        echo ✓ %%s\package.json 存在
    ) else (
        echo ✗ %%s\package.json 不存在
    )
)

echo.
echo ========================================
echo 数据库配置验证
echo ========================================

echo MySQL服务配置:
echo - service-registry: ir_engine_registry
echo - api-gateway: api_gateway
echo - user-service: ir_engine_users
echo - project-service: ir_engine_projects
echo - asset-service: ir_engine_assets
echo - render-service: ir_engine_render
echo - ai-model-service: ai_model_service
echo - monitoring-service: monitoring

echo.
echo PostgreSQL服务配置:
echo - scene-generation-service: scene_generation
echo - asset-library-service: asset_library
echo - collaboration-service: digital_human_rag
echo - knowledge-service: knowledge_service
echo - rag-engine: digital_human_rag
echo - binding-service: digital_human_rag
echo - scene-template-service: scene_templates

echo.
echo ========================================
echo 网络配置验证
echo ========================================

echo 网络配置:
echo - 网络名称: digital-human-network
echo - 驱动类型: bridge
echo - 子网: 172.20.0.0/16

echo.
echo ========================================
echo 环境变量验证
echo ========================================

REM 检查环境变量文件
if exist ".env" (
    echo ✓ .env 文件存在
) else (
    echo ⚠️  .env 文件不存在，将使用默认配置
    if exist ".env.windows.example" (
        echo 建议复制 .env.windows.example 为 .env 并修改配置
    )
)

echo.
echo ========================================
echo 共享模块验证
echo ========================================

if exist "shared" (
    echo ✓ shared 目录存在
    if exist "shared\init-scripts" (
        echo ✓ shared\init-scripts 目录存在
        if exist "shared\init-scripts\mysql" (
            echo ✓ shared\init-scripts\mysql 目录存在
        ) else (
            echo ⚠️  shared\init-scripts\mysql 目录不存在
        )
    ) else (
        echo ⚠️  shared\init-scripts 目录不存在
    )
) else (
    echo ⚠️  shared 目录不存在
)

echo.
echo ========================================
echo 前端配置验证
echo ========================================

if exist "..\editor" (
    echo ✓ editor 目录存在
    if exist "..\editor\Dockerfile" (
        echo ✓ editor\Dockerfile 存在
    ) else (
        echo ✗ editor\Dockerfile 不存在
    )
    if exist "..\editor\package.json" (
        echo ✓ editor\package.json 存在
    ) else (
        echo ✗ editor\package.json 不存在
    )
) else (
    echo ✗ editor 目录不存在
)

echo.
echo ========================================
echo 配置验证完成
echo ========================================

echo.
echo 验证结果总结:
echo - Docker Compose语法: 正确
echo - 端口配置: 已检查
echo - 服务文件: 已验证
echo - 数据库配置: 已确认
echo - 网络配置: 已验证
echo - 环境变量: 已检查
echo - 共享模块: 已验证
echo - 前端配置: 已验证

echo.
echo 如果所有检查都通过，可以运行以下命令启动系统:
echo start-windows-complete.bat

echo.
pause
